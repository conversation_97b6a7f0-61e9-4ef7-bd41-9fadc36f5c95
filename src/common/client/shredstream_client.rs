use std::sync::Arc;

use anyhow::{Context, Result};
use tokio::{sync::Mutex, time::timeout};
use tonic::codec::Streaming;

use crate::{
    common::client::config::ShredstreamClientConfig,
    generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient},
    utils::subscriptions::create_subscribe_request,
};

pub struct ShredstreamClient {
    endpoint: String,
    config: ShredstreamClientConfig,
    shared_stream: Arc<Mutex<Option<Arc<Mutex<Streaming<Entry>>>>>>,
    connection_lock: Arc<Mutex<()>>,
    grpc_client: Arc<Mutex<Option<ShredstreamProxyClient<tonic::transport::Channel>>>>,
}

impl ShredstreamClient {
    pub fn new(endpoint: String, config: ShredstreamClientConfig) -> Self {
        Self {
            endpoint,
            config,
            shared_stream: Arc::new(Mutex::new(None)),
            connection_lock: Arc::new(Mutex::new(())),
            grpc_client: Arc::new(Mutex::new(None)),
        }
    }

    pub async fn subscribe(&self) -> Result<Arc<Mutex<Streaming<Entry>>>> {
        if let Some(existing_stream) = self.get_existing_stream().await {
            return Ok(existing_stream);
        }

        let _connection_guard = self.connection_lock.lock().await;

        if let Some(existing_stream) = self.get_existing_stream().await {
            return Ok(existing_stream);
        }

        match self.connect_and_subscribe_internal().await {
            Ok(stream) => {
                let shared_stream = Arc::new(Mutex::new(stream));
                {
                    let mut stream_guard = self.shared_stream.lock().await;
                    *stream_guard = Some(shared_stream.clone());
                }
                Ok(shared_stream)
            }
            Err(e) => {
                self.cleanup_connection().await;
                Err(e)
            }
        }
    }

    pub async fn reset(&self) {
        self.cleanup_connection().await;
    }

    async fn get_existing_stream(&self) -> Option<Arc<Mutex<Streaming<Entry>>>> {
        let stream_guard = self.shared_stream.lock().await;
        stream_guard.clone()
    }

    async fn cleanup_connection(&self) {
        let mut client_guard = self.grpc_client.lock().await;
        *client_guard = None;

        let mut stream_guard = self.shared_stream.lock().await;
        *stream_guard = None;
    }

    async fn handle_subscribe_error(&self, message: String) -> Result<Streaming<Entry>> {
        self.cleanup_connection().await;
        Err(anyhow::anyhow!(message))
    }

    async fn connect_and_subscribe_internal(&self) -> Result<Streaming<Entry>> {
        let connect_future = ShredstreamProxyClient::connect(self.endpoint.clone());
        let client = timeout(self.config.connect_timeout, connect_future)
            .await
            .context("Connect timeout exceeded")?
            .context("Failed to connect to shredstream endpoint")?;

        {
            let mut client_guard = self.grpc_client.lock().await;
            *client_guard = Some(client.clone());
        }

        let request = create_subscribe_request(self.config.filters.accounts.as_ref());
        let mut client_for_subscribe = client.clone();
        let subscribe_future = client_for_subscribe.subscribe_entries(request);

        let response = match timeout(self.config.subscribe_timeout, subscribe_future).await {
            Ok(Ok(response)) => response,
            Ok(Err(e)) => return self.handle_subscribe_error(format!("Subscribe failed: {}", e)).await,
            Err(_) => return self.handle_subscribe_error("Subscribe timeout exceeded".to_string()).await,
        };

        Ok(response.into_inner())
    }
}
