/Users/<USER>/PJ/shreder/target/debug/deps/shreder-56d674ad773f51bc.d: src/lib.rs src/common/mod.rs src/common/broadcaster.rs src/common/client/mod.rs src/common/client/config.rs src/common/client/shredstream_client.rs src/common/monitor.rs src/common/processor.rs src/common/server.rs src/config/mod.rs src/config/app.rs src/config/logger.rs src/core/mod.rs src/core/config.rs src/core/logger.rs src/generated.rs src/utils/mod.rs src/utils/subscriptions.rs src/generated/shredstream.rs

/Users/<USER>/PJ/shreder/target/debug/deps/libshreder-56d674ad773f51bc.rmeta: src/lib.rs src/common/mod.rs src/common/broadcaster.rs src/common/client/mod.rs src/common/client/config.rs src/common/client/shredstream_client.rs src/common/monitor.rs src/common/processor.rs src/common/server.rs src/config/mod.rs src/config/app.rs src/config/logger.rs src/core/mod.rs src/core/config.rs src/core/logger.rs src/generated.rs src/utils/mod.rs src/utils/subscriptions.rs src/generated/shredstream.rs

src/lib.rs:
src/common/mod.rs:
src/common/broadcaster.rs:
src/common/client/mod.rs:
src/common/client/config.rs:
src/common/client/shredstream_client.rs:
src/common/monitor.rs:
src/common/processor.rs:
src/common/server.rs:
src/config/mod.rs:
src/config/app.rs:
src/config/logger.rs:
src/core/mod.rs:
src/core/config.rs:
src/core/logger.rs:
src/generated.rs:
src/utils/mod.rs:
src/utils/subscriptions.rs:
src/generated/shredstream.rs:

# env-dep:CARGO_PKG_NAME=shreder
